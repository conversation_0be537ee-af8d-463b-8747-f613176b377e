import pypandoc
import os

def save_markdown_as_word(markdown_text: str, output_filename: str):
    """
    将Markdown格式的字符串转换为Word（.docx）文件并保存到本地。

    参数:
        markdown_text (str): 包含Markdown内容的字符串。
        output_filename (str): 输出的Word文件名，建议以 .docx 结尾。
    """
    # 确保输出文件名以 .docx 结尾
    if not output_filename.endswith('.docx'):
        output_filename += '.docx'

    print(f"正在转换Markdown为Word文档...")
    try:
        # 使用 pypandoc 进行转换
        # 'md' 是源格式 (markdown)
        # 'docx' 是目标格式
        pypandoc.convert_text(
            source=markdown_text,
            to='docx',
            format='md',
            outputfile=output_filename
        )
        print(f"✅ 成功！文件已保存为: {os.path.abspath(output_filename)}")
    except FileNotFoundError:
        print("❌ 错误: 找不到 Pandoc。")
        print("请确保你已经安装了 Pandoc 并且它在系统的 PATH 环境变量中。")
        print("Pandoc 安装教程: https://pandoc.org/installing.html")
    except Exception as e:
        print(f"❌ 转换过程中发生未知错误: {e}")

# --- 主程序逻辑 ---
if __name__ == "__main__":
    # 1. 假设你已经运行了 deep_researcher 并获取了最终报告
    # 在实际使用中，你需要用下面这行替换掉示例Markdown
    # final_result = deep_researcher.invoke(...)
    # markdown_report = final_result['final_report']

    # 这里我们用一个示例Markdown字符串来演示
    markdown_report = """
# 关于大语言模型（LLM）在金融领域的应用研究报告

**日期**: 2025年08月06日

## 1. 摘要

本报告深入探讨了大型语言模型（LLM）在金融服务行业的应用潜力、当前挑战和未来趋势。研究发现，LLM在客户服务、风险管理、市场分析等方面展现出巨大价值。

## 2. 主要发现

* **自动化投顾**: LLM能够根据客户的风险偏好提供个性化的投资建议。
* **情绪分析**: 通过分析新闻和社交媒体，LLM可以实时评估市场情绪。
* **合规与监管**: 自动化处理合规文件，降低人为错误。

## 3. 挑战与风险

1.  **数据安全**: 金融数据高度敏感，存在泄露风险。
2.  **模型幻觉**: 模型可能生成不准确或虚假信息，导致决策失误。
3.  **监管不确定性**: 相关法规尚不完善。

## 4. 结论

尽管存在挑战，但LLM无疑将重塑金融行业的未来。我们建议企业采取分阶段、小范围试点的方式，逐步推进LLM的应用。
"""

    # 2. 定义你想要保存的Word文件名
    output_file_name = "final_research_report"

    # 3. 调用函数进行转换和保存
    save_markdown_as_word(markdown_report, output_file_name)