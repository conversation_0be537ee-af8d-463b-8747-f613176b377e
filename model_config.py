"""
模型配置文件
定义各种LLM模型的配置参数，包括API地址、模型名称等
"""
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """模型配置类"""
    provider: str
    model_name: str
    api_key_env: str
    base_url: Optional[str] = None
    default_temperature: float = 0.7
    default_max_tokens: int = 4096


# 模型配置映射
MODEL_CONFIGS = {
    # DeepSeek 模型
    "deepseek-chat": ModelConfig(
        provider="deepseek",
        model_name="deepseek-chat",
        api_key_env="DEEPSEEK_API_KEY",
        base_url="https://api.deepseek.com",
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    
    # OpenAI 模型
    "gpt-4o": ModelConfig(
        provider="openai",
        model_name="gpt-4o",
        api_key_env="OPENAI_API_KEY",
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    "gpt-4o-mini": ModelConfig(
        provider="openai",
        model_name="gpt-4o-mini",
        api_key_env="OPENAI_API_KEY",
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    
    # Anthropic 模型
    "claude-3-5-sonnet-20241022": ModelConfig(
        provider="anthropic",
        model_name="claude-3-5-sonnet-20241022",
        api_key_env="ANTHROPIC_API_KEY",
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    
    # Google Gemini 模型 (原生接口)
    "gemini-1.5-pro": ModelConfig(
        provider="google_genai",
        model_name="gemini-1.5-pro",
        api_key_env="GOOGLE_API_KEY",
        base_url=None,
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    "gemini-1.5-flash": ModelConfig(
        provider="google_genai",
        model_name="gemini-1.5-flash",
        api_key_env="GOOGLE_API_KEY",
        base_url=None,
        default_temperature=0.7,
        default_max_tokens=4096
    ),

    # Google Gemini 模型 (中转接口) - 使用 OpenAI 兼容接口
    "gemini-2.5-pro": ModelConfig(
        provider="google_genai",  # 使用 OpenAI 兼容接口访问中转服务
        model_name="gemini-2.5-pro",
        api_key_env="GOOGLE_API_KEY",
        base_url=os.getenv("GEMINI_BASE_URL", "https://gohadknkslgl.ap-southeast-1.clawcloudrun.com"),
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    "gemini-2.5-flash": ModelConfig(
        provider="google_genai",  # 使用 OpenAI 兼容接口访问中转服务
        model_name="gemini-2.5-flash",
        api_key_env="GOOGLE_API_KEY",
        base_url=os.getenv("GEMINI_BASE_URL", "https://gohadknkslgl.ap-southeast-1.clawcloudrun.com"),
        default_temperature=0.7,
        default_max_tokens=4096
    ),

    # 如果你想使用原生 Google API 但通过中转访问，可以使用这些配置
    "gemini-1.5-pro-proxy": ModelConfig(
        provider="google_genai",
        model_name="gemini-1.5-pro",
        api_key_env="GOOGLE_API_KEY",
        base_url=os.getenv("GEMINI_BASE_URL"),  # 某些中转服务支持 Google GenAI 格式
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    
    # Kimi (Moonshot) 模型
    "moonshot-v1-8k": ModelConfig(
        provider="openai",  # Kimi 使用 OpenAI 兼容接口
        model_name="moonshot-v1-8k",
        api_key_env="MOONSHOT_API_KEY",
        base_url="https://api.moonshot.cn/v1",
        default_temperature=0.7,
        default_max_tokens=8192
    ),
    "moonshot-v1-32k": ModelConfig(
        provider="openai",
        model_name="moonshot-v1-32k",
        api_key_env="MOONSHOT_API_KEY",
        base_url="https://api.moonshot.cn/v1",
        default_temperature=0.7,
        default_max_tokens=32768
    ),
    
    # GLM 模型 (智谱AI)
    "glm-4": ModelConfig(
        provider="openai",  # GLM 使用 OpenAI 兼容接口
        model_name="glm-4",
        api_key_env="GLM_API_KEY",
        base_url="https://open.bigmodel.cn/api/paas/v4",
        default_temperature=0.7,
        default_max_tokens=4096
    ),
    "glm-4-flash": ModelConfig(
        provider="openai",
        model_name="glm-4-flash",
        api_key_env="GLM_API_KEY",
        base_url="https://open.bigmodel.cn/api/paas/v4",
        default_temperature=0.7,
        default_max_tokens=4096
    ),
}


def get_model_config(model_name: str) -> ModelConfig:
    """获取模型配置"""
    if model_name not in MODEL_CONFIGS:
        raise ValueError(f"不支持的模型: {model_name}. 支持的模型: {list(MODEL_CONFIGS.keys())}")
    return MODEL_CONFIGS[model_name]


def get_available_models() -> list[str]:
    """获取所有可用的模型列表"""
    return list(MODEL_CONFIGS.keys())


def create_model_kwargs(model_name: str, **override_kwargs) -> Dict[str, Any]:
    """创建模型初始化参数"""
    config = get_model_config(model_name)

    # API Key
    api_key = os.getenv(config.api_key_env)
    if not api_key:
        raise ValueError(f"未找到 {config.api_key_env} 环境变量")

    # 根据不同的提供商创建不同的参数
    if config.provider == "openai":
        # OpenAI 兼容接口 (包括中转的 Gemini、Kimi、GLM 等)
        kwargs = {
            "model": config.model_name,
            "model_provider": "openai",
            "api_key": api_key,
            "temperature": override_kwargs.get("temperature", config.default_temperature),
            "max_tokens": override_kwargs.get("max_tokens", config.default_max_tokens),
        }
        if config.base_url:
            kwargs["base_url"] = config.base_url

    elif config.provider == "anthropic":
        # Anthropic Claude
        kwargs = {
            "model": config.model_name,
            "model_provider": "anthropic",
            "api_key": api_key,
            "temperature": override_kwargs.get("temperature", config.default_temperature),
            "max_tokens": override_kwargs.get("max_tokens", config.default_max_tokens),
        }

    elif config.provider == "google_genai":
        # Google Gemini (原生接口)
        kwargs = {
            "model": config.model_name,
            "model_provider": "google_genai",
            "api_key": api_key,
            "temperature": override_kwargs.get("temperature", config.default_temperature),
            "max_tokens": override_kwargs.get("max_tokens", config.default_max_tokens),
        }

    elif config.provider == "deepseek":
        # DeepSeek
        kwargs = {
            "model": config.model_name,
            "model_provider": "deepseek",
            "api_key": api_key,
            "temperature": override_kwargs.get("temperature", config.default_temperature),
            "max_tokens": override_kwargs.get("max_tokens", config.default_max_tokens),
        }
        if config.base_url:
            kwargs["base_url"] = config.base_url

    else:
        raise ValueError(f"不支持的模型提供商: {config.provider}")

    # 应用覆盖参数
    kwargs.update(override_kwargs)

    return kwargs


def validate_model_environment(model_name: str) -> bool:
    """验证模型环境配置是否正确"""
    try:
        config = get_model_config(model_name)
        api_key = os.getenv(config.api_key_env)
        return api_key is not None
    except ValueError:
        return False
